import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { ScrollArea } from '../ui/scroll-area'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Label } from '../ui/label'
import {
  History,
  Edit,
  Trash2,
  Calendar,
  TrendingUp,
  TrendingDown,
  Minus,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { databaseApi } from '../../lib/api'
import { useUIStore } from '../../store/uiStore'
// {{ AURA-X: Add - 导入国际化上下文. Approval: 寸止(ID:1738157400). }}
import { useLanguage } from '../../contexts/LanguageContext'
import type { AreaMetric, AreaMetricRecord } from '../../../../shared/types'

interface AreaMetricHistoryProps {
  metric: AreaMetric
  className?: string
}

interface EditRecordDialogProps {
  record: AreaMetricRecord | null
  isOpen: boolean
  onClose: () => void
  onSave: (record: AreaMetricRecord) => void
}

function EditRecordDialog({ record, isOpen, onClose, onSave }: EditRecordDialogProps) {
  const [value, setValue] = useState('')
  const [note, setNote] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { addNotification } = useUIStore()

  useEffect(() => {
    if (record) {
      setValue(record.value)
      setNote(record.note || '')
    }
  }, [record])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!record) return

    setIsSubmitting(true)
    try {
      const result = await databaseApi.updateAreaMetricRecord({
        id: record.id,
        updates: {
          value: value.trim(),
          note: note.trim() || undefined
        }
      })

      if (result.success) {
        addNotification({
          type: 'success',
          title: 'Record Updated',
          message: 'Metric record has been updated successfully'
        })
        onSave(result.data)
        onClose()
      } else {
        throw new Error(result.error || 'Failed to update record')
      }
    } catch (error) {
      console.error('Failed to update area metric record:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Update',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Edit Record</DialogTitle>
          <DialogDescription>
            Modify the metric record details
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="edit-value">Value</Label>
            <Input
              id="edit-value"
              type="text"
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder="Enter value"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-note">Note</Label>
            <Textarea
              id="edit-note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add a note..."
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export function AreaMetricHistory({ metric, className }: AreaMetricHistoryProps) {
  const [records, setRecords] = useState<AreaMetricRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [editingRecord, setEditingRecord] = useState<AreaMetricRecord | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const { addNotification } = useUIStore()
  // {{ AURA-X: Add - 国际化支持. Approval: 寸止(ID:1738157400). }}
  const { t } = useLanguage()

  useEffect(() => {
    loadRecords()
  }, [metric.id])

  const loadRecords = async () => {
    setIsLoading(true)
    try {
      const result = await databaseApi.getAreaMetricRecords(metric.id, 20)
      if (result.success) {
        setRecords(result.data || [])
      } else {
        throw new Error(result.error || 'Failed to load records')
      }
    } catch (error) {
      console.error('Failed to load area metric records:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Load Records',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleEditRecord = (record: AreaMetricRecord) => {
    setEditingRecord(record)
    setIsEditDialogOpen(true)
  }

  const handleDeleteRecord = async (record: AreaMetricRecord) => {
    try {
      const result = await databaseApi.deleteAreaMetricRecord(record.id)
      if (result.success) {
        setRecords(prev => prev.filter(r => r.id !== record.id))
        addNotification({
          type: 'success',
          title: 'Record Deleted',
          message: 'Metric record has been deleted successfully'
        })
      } else {
        throw new Error(result.error || 'Failed to delete record')
      }
    } catch (error) {
      console.error('Failed to delete area metric record:', error)
      addNotification({
        type: 'error',
        title: 'Failed to Delete',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    }
  }

  const handleRecordSaved = (updatedRecord: AreaMetricRecord) => {
    setRecords(prev => prev.map(r => r.id === updatedRecord.id ? updatedRecord : r))
  }

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-3 w-3 text-green-600" />
    if (current < previous) return <TrendingDown className="h-3 w-3 text-red-600" />
    return <Minus className="h-3 w-3 text-muted-foreground" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          {t('pages.areas.detail.metricHistory.titleWithName', { name: metric.name })}
        </CardTitle>
        <CardDescription>
          {t('pages.areas.detail.metricHistory.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8 text-muted-foreground">
            {t('pages.areas.detail.metricHistory.loadingRecords')}
          </div>
        ) : records.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{t('pages.areas.detail.metricHistory.noRecordsYet')}</p>
            <p className="text-xs mt-1">{t('pages.areas.detail.metricHistory.startRecording')}</p>
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-3">
              {records.map((record, index) => {
                const currentValue = parseFloat(record.value)
                const previousValue = index < records.length - 1 ? parseFloat(records[index + 1].value) : null
                
                return (
                  <div
                    key={record.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">
                          {record.value}
                          {metric.unit && <span className="text-muted-foreground ml-1">{metric.unit}</span>}
                        </span>
                        {previousValue !== null && getTrendIcon(currentValue, previousValue)}
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(record.recordedAt)}</span>
                      </div>
                      
                      {record.note && (
                        <p className="text-sm text-muted-foreground mt-1 truncate">
                          {record.note}
                        </p>
                      )}
                    </div>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditRecord(record)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteRecord(record)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>

      <EditRecordDialog
        record={editingRecord}
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false)
          setEditingRecord(null)
        }}
        onSave={handleRecordSaved}
      />
    </Card>
  )
}

export default AreaMetricHistory
